import 'package:flutter/material.dart';

/// 智能加载组件 - 根据缓存状态显示不同的加载提示
class SmartLoadingWidget extends StatefulWidget {
  final String loadingText;
  final bool isFromCache;
  final Color? color;

  const SmartLoadingWidget({
    super.key,
    this.loadingText = 'Loading...',
    this.isFromCache = false,
    this.color,
  });

  @override
  State<SmartLoadingWidget> createState() => _SmartLoadingWidgetState();
}

class _SmartLoadingWidgetState extends State<SmartLoadingWidget>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // 根据是否使用缓存显示不同的加载指示器
            widget.isFromCache
                ? _buildCacheLoadingIndicator()
                : _buildNetworkLoadingIndicator(),
            const SizedBox(height: 16),
            Text(
              widget.isFromCache ? 'Loading from cache...' : widget.loadingText,
              style: TextStyle(
                fontSize: 16,
                color: widget.color ?? Colors.grey[600],
                fontWeight: FontWeight.w500,
              ),
            ),
            if (!widget.isFromCache) ...[
              const SizedBox(height: 8),
              Text(
                'Fetching data from server...',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[500],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildNetworkLoadingIndicator() {
    return CircularProgressIndicator(
      valueColor: AlwaysStoppedAnimation<Color>(
        widget.color ?? Colors.blue,
      ),
    );
  }

  Widget _buildCacheLoadingIndicator() {
    return Container(
      width: 40,
      height: 40,
      decoration: BoxDecoration(
        color: (widget.color ?? Colors.green).withOpacity(0.1),
        shape: BoxShape.circle,
      ),
      child: Icon(
        Icons.flash_on,
        color: widget.color ?? Colors.green,
        size: 24,
      ),
    );
  }
}

/// 快速加载提示组件 - 用于显示缓存命中的情况
class QuickLoadIndicator extends StatelessWidget {
  final String message;
  final Color? color;

  const QuickLoadIndicator({
    super.key,
    this.message = 'Loaded from cache',
    this.color,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: (color ?? Colors.green).withOpacity(0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: (color ?? Colors.green).withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.flash_on,
            size: 16,
            color: color ?? Colors.green,
          ),
          const SizedBox(width: 4),
          Text(
            message,
            style: TextStyle(
              fontSize: 12,
              color: color ?? Colors.green,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }
}
