import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../services/api_service.dart';
import 'dart:convert';

class ApiTestPage extends StatefulWidget {
  const ApiTestPage({super.key});

  @override
  State<ApiTestPage> createState() => _ApiTestPageState();
}

class _ApiTestPageState extends State<ApiTestPage> {
  String _result = '';
  bool _isLoading = false;

  void _copyToClipboard() {
    if (_result.isNotEmpty) {
      Clipboard.setData(ClipboardData(text: _result));
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('API result copied to clipboard'),
          duration: Duration(seconds: 2),
        ),
      );
    }
  }

  void _showFullScreenResult() {
    if (_result.isNotEmpty) {
      Navigator.of(context).push(
        MaterialPageRoute(
          builder: (context) => _FullScreenResultPage(result: _result),
        ),
      );
    }
  }

  Future<void> _testApi(String testName, Future<Map<String, dynamic>> Function() apiCall) async {
    setState(() {
      _isLoading = true;
      _result = 'Testing $testName...';
    });

    try {
      final result = await apiCall();
      setState(() {
        _result = '$testName Result:\n${const JsonEncoder.withIndent('  ').convert(result)}';
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _result = '$testName Error: $e';
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('API Test', style: TextStyle(color: Colors.white)),
        backgroundColor: Colors.deepPurple,
        iconTheme: const IconThemeData(color: Colors.white),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            // Test buttons - Essential APIs only
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: [
                // Core APIs for app flow
                ElevatedButton(
                  onPressed: _isLoading ? null : () => _testApi('GET /area', ApiService.getAreas),
                  child: const Text('Test Areas'),
                ),
                ElevatedButton(
                  onPressed: _isLoading ? null : () => _testApi('GET /Line/3', () => ApiService.getLineById(3)),
                  child: const Text('Test Line 3'),
                ),
                ElevatedButton(
                  onPressed: _isLoading ? null : () => _testApi('GET /Line/6', () => ApiService.getLineById(6)),
                  child: const Text('Test Line 6'),
                ),
                ElevatedButton(
                  onPressed: _isLoading ? null : () => _testApi('GET /machine/downtimeCount/3', () => ApiService.getMachinesByLine(3)),
                  child: const Text('Machines Line 3'),
                ),
                ElevatedButton(
                  onPressed: _isLoading ? null : () => _testApi('GET /machine/downtimeCount/6', () => ApiService.getMachinesByLine(6)),
                  child: const Text('Machines Line 6'),
                ),
                ElevatedButton(
                  onPressed: _isLoading ? null : () => _testApi('GET /Downtime', ApiService.getAllDowntimes),
                  child: const Text('All Downtimes'),
                ),
                ElevatedButton(
                  onPressed: _isLoading ? null : () => _testApi('Test Connection', () async {
                    final isConnected = await ApiService.testConnection();
                    return {'success': isConnected, 'message': isConnected ? 'Connected' : 'Failed'};
                  }),
                  child: const Text('Test Connection'),
                ),
              ],
            ),
            
            const SizedBox(height: 20),
            
            // Current API URL display
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.blue.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.blue.withOpacity(0.3)),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text('Current API URL:', style: TextStyle(fontWeight: FontWeight.bold)),
                  Text(ApiService.baseUrl, style: const TextStyle(fontFamily: 'monospace')),
                ],
              ),
            ),
            
            const SizedBox(height: 20),
            
            // Results section header with action buttons
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'API Results:',
                  style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
                ),
                if (_result.isNotEmpty && !_isLoading)
                  Row(
                    children: [
                      IconButton(
                        onPressed: _copyToClipboard,
                        icon: const Icon(Icons.copy, size: 20),
                        tooltip: 'Copy to clipboard',
                      ),
                      IconButton(
                        onPressed: _showFullScreenResult,
                        icon: const Icon(Icons.fullscreen, size: 20),
                        tooltip: 'Full screen view',
                      ),
                    ],
                  ),
              ],
            ),
            const SizedBox(height: 8),
            Expanded(
              child: Container(
                width: double.infinity,
                decoration: BoxDecoration(
                  color: Colors.grey.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.grey.withOpacity(0.3)),
                ),
                child: _isLoading
                    ? const Center(child: CircularProgressIndicator())
                    : _result.isEmpty
                        ? const Center(
                            child: Text(
                              'Click a button above to test API...',
                              style: TextStyle(
                                fontSize: 14,
                                color: Colors.grey,
                                fontStyle: FontStyle.italic,
                              ),
                            ),
                          )
                        : Scrollbar(
                            thumbVisibility: true,
                            child: SingleChildScrollView(
                              padding: const EdgeInsets.all(16),
                              child: SelectableText(
                                _result,
                                style: const TextStyle(
                                  fontFamily: 'monospace',
                                  fontSize: 11,
                                  height: 1.4,
                                ),
                              ),
                            ),
                          ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

// Full screen result viewer page
class _FullScreenResultPage extends StatelessWidget {
  final String result;

  const _FullScreenResultPage({required this.result});

  void _copyToClipboard(BuildContext context) {
    Clipboard.setData(ClipboardData(text: result));
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('API result copied to clipboard'),
        duration: Duration(seconds: 2),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('API Result - Full View', style: TextStyle(color: Colors.white)),
        backgroundColor: Colors.deepPurple,
        iconTheme: const IconThemeData(color: Colors.white),
        actions: [
          IconButton(
            onPressed: () => _copyToClipboard(context),
            icon: const Icon(Icons.copy),
            tooltip: 'Copy to clipboard',
          ),
        ],
      ),
      body: Container(
        width: double.infinity,
        height: double.infinity,
        padding: const EdgeInsets.all(16),
        child: Scrollbar(
          thumbVisibility: true,
          child: SingleChildScrollView(
            child: SelectableText(
              result,
              style: const TextStyle(
                fontFamily: 'monospace',
                fontSize: 12,
                height: 1.5,
              ),
            ),
          ),
        ),
      ),
    );
  }
}


