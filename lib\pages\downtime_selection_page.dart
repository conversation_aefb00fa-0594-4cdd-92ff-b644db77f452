import 'package:flutter/material.dart';
import 'downtime_detail_page.dart';
import '../services/data_cache_service.dart';
import '../widgets/smart_loading_widget.dart';

class DowntimeSelectionPage extends StatefulWidget {
  final int machineId;
  final String machineName;
  final int downtimeCount;
  final Color machineColor;
  final String? lineName;
  final String? areaName;

  const DowntimeSelectionPage({
    super.key,
    required this.machineId,
    required this.machineName,
    required this.downtimeCount,
    required this.machineColor,
    this.lineName,
    this.areaName,
  });

  @override
  State<DowntimeSelectionPage> createState() => _DowntimeSelectionPageState();
}

class _DowntimeSelectionPageState extends State<DowntimeSelectionPage> {
  final ScrollController _scrollController = ScrollController();
  bool _showScrollToTop = false;
  List<Map<String, dynamic>> _downtimes = [];
  bool _isLoading = true;
  String? _error;

  Future<void> _loadDowntimes() async {
    try {
      setState(() {
        _isLoading = true;
        _error = null;
      });

  
      if (widget.downtimeCount == 0) {
        print('🔍 Machine downtimeCount is 0, following business logic, not calling API'); 
        setState(() {
          _downtimes = [];
          _isLoading = false;
        });
        return;
      }

      final cacheService = DataCacheService();

      final result = await cacheService.getAllDowntimes();
      print('🔍 Downtime API response: $result'); // Debug info

      if (result['success']) {
        final List<dynamic> downtimeData = result['data'] ?? [];
        print('🔍 Retrieved Downtime count: ${downtimeData.length}'); // Debug info
        
        setState(() {
          // First filter downtimes belonging to current Machine, then convert format
          final filteredDowntimes = downtimeData.where((downtime) {
            final machineId = downtime['machineId'];
            print('🔍 Checking Downtime ${downtime['id']}: machineId=$machineId, current Machine=${widget.machineId}');
            return machineId == widget.machineId;
          }).toList();
          
          print('🔍 Filtered Downtime count: ${filteredDowntimes.length} (original: ${downtimeData.length})');
          
          _downtimes = filteredDowntimes.map<Map<String, dynamic>>((downtime) {
            return {
              'id': downtime['id']?.toString() ?? 'Unknown',
              'title': downtime['remark']?.isNotEmpty == true ? downtime['remark'] : 'Downtime Issue',
              'priority': downtime['priority'] ?? 'High',
              'status': downtime['status'] ?? 'LOCK',
              'description': downtime['remark'] ?? 'Machine downtime issue',
              'createdDate': downtime['startDate'] ?? DateTime.now().toIso8601String(),
              'machineId': downtime['machineId'],
              'rawData': downtime, // Save original data for detail page
            };
          }).toList();
          _isLoading = false;
        });
      } else {
        throw Exception(result['message'] ?? 'Failed to load downtimes');
      }
    } catch (e) {
      print('❌ Failed to load Downtime: $e'); // Debug info
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });
    }
  }

  @override
  void initState() {
    super.initState();
    _loadDowntimes(); // Load real API data
    _scrollController.addListener(_scrollListener);
  }

  void _scrollListener() {
    if (_scrollController.offset > 200) {
      if (!_showScrollToTop) {
        setState(() {
          _showScrollToTop = true;
        });
      }
    } else {
      if (_showScrollToTop) {
        setState(() {
          _showScrollToTop = false;
        });
      }
    }
  }

  void _selectDowntime(BuildContext context, Map<String, dynamic> downtime) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => DowntimeDetailPage(
          downtime: downtime,
          machineName: widget.machineName,
          machineColor: Colors.purple, // Downtime Details page uses purple AppBar
          lineName: widget.lineName,
          areaName: widget.areaName,
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {

    return Scaffold(
      backgroundColor: Colors.grey[100],
      appBar: AppBar(
        title: Text(
          widget.machineName,
          style: const TextStyle(
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: widget.machineColor,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.pop(context),
        ),
        actions: [
          // Refresh downtimes button
          IconButton(
            icon: const Icon(Icons.refresh, color: Colors.white),
            tooltip: 'Refresh Downtimes',
            onPressed: () async {
              final cacheService = DataCacheService();
              cacheService.clearDowntimeCache();
              _loadDowntimes();
              
              if (mounted) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Downtimes refreshed!'),
                    duration: Duration(seconds: 2),
                    backgroundColor: Colors.purple,
                  ),
                );
              }
            },
          ),
          IconButton(
            icon: const Icon(Icons.home, color: Colors.white),
            onPressed: () {
              Navigator.popUntil(context, (route) => route.isFirst);
            },
          ),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.all(12.0),
        child: _isLoading
            ? const SmartLoadingWidget(
                loadingText: 'Loading downtimes...',
                color: Colors.purple,
              )
            : _error != null
            ? Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Icon(Icons.error_outline, size: 64, color: Colors.red),
                    const SizedBox(height: 16),
                    const Text('Error loading downtimes', style: TextStyle(fontSize: 18, color: Colors.red)),
                    const SizedBox(height: 8),
                    Text(_error!, style: const TextStyle(fontSize: 14, color: Colors.grey)),
                    const SizedBox(height: 16),
                    ElevatedButton(
                      onPressed: _loadDowntimes,
                      child: const Text('Retry'),
                    ),
                  ],
                ),
              )
            : _downtimes.isEmpty
            ? const Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(Icons.check_circle, size: 64, color: Colors.green),
                    SizedBox(height: 16),
                    Text('No downtimes found', style: TextStyle(fontSize: 18, color: Colors.grey)),
                    SizedBox(height: 8),
                    Text('This machine is running normally', style: TextStyle(fontSize: 14, color: Colors.grey)),
                  ],
                ),
              )
            : GridView.builder(
                controller: _scrollController,
                gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 2,
                  crossAxisSpacing: 12,
                  mainAxisSpacing: 12,
                  childAspectRatio: 1.0,
                ),
                itemCount: _downtimes.length,
                itemBuilder: (context, index) {
                  final downtime = _downtimes[index];
                  
                  return Card(
                    elevation: 4,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: InkWell(
                      onTap: () => _selectDowntime(context, downtime),
                      borderRadius: BorderRadius.circular(12),
                      child: Container(
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(12),
                          gradient: LinearGradient(
                            colors: [
                              Colors.purple.withOpacity(0.1), // Downtime page uses purple
                              Colors.purple.withOpacity(0.05),
                            ],
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                          ),
                        ),
                        child: Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              // Downtime icon
                              Container(
                                width: 50,
                                height: 50,
                                decoration: BoxDecoration(
                                  color: Colors.purple.withOpacity(0.2), // Downtime page icon background uses purple
                                  borderRadius: BorderRadius.circular(25),
                                ),
                                child: Icon(
                                  Icons.warning,
                                  color: Colors.purple, // Downtime page icon uses purple
                                  size: 26,
                                ),
                              ),
                              const SizedBox(height: 12),
                              
                              // Downtime ID - centered
                              Text(
                                downtime['id'],
                                style: const TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.black87,
                                ),
                                textAlign: TextAlign.center,
                              ),
                              const SizedBox(height: 4),
                              
                              // Downtime type - centered
                              Text(
                                downtime['title'],
                                style: TextStyle(
                                  fontSize: 12,
                                  color: Colors.grey[600],
                                  fontWeight: FontWeight.w500,
                                ),
                                textAlign: TextAlign.center,
                                maxLines: 2,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  );
                },
              ),
      ),
      floatingActionButton: _showScrollToTop
          ? FloatingActionButton(
              onPressed: () {
                _scrollController.animateTo(
                  0,
                  duration: const Duration(milliseconds: 500),
                  curve: Curves.easeInOut,
                );
              },
              backgroundColor: widget.machineColor,
              child: const Icon(Icons.keyboard_arrow_up, color: Colors.white),
            )
          : null,
    );
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }
}
