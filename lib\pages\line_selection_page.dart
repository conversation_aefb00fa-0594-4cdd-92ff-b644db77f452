import 'package:flutter/material.dart';
import 'machine_selection_page.dart';
import '../services/system_notification_service.dart';
import '../services/data_cache_service.dart';

class LineSelectionPage extends StatefulWidget {
  final int areaId;
  final String areaName;
  final Color areaColor;

  const LineSelectionPage({
    super.key,
    required this.areaId,
    required this.areaName,
    required this.areaColor,
  });

  @override
  State<LineSelectionPage> createState() => _LineSelectionPageState();
}

class _LineSelectionPageState extends State<LineSelectionPage> {
  final ScrollController _scrollController = ScrollController();
  bool _showScrollToTop = false;
  bool _isLoading = true;
  List<Map<String, dynamic>> _lines = [];
  String? _error;

  // Note: Line colors are now managed individually per line

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_scrollListener);
    _loadLines();
  }

  Future<void> _loadLines() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final cacheService = DataCacheService();
      
      // Use cache service to get Line data
      final result = await cacheService.getLinesByArea(widget.areaId);
      print('🔍 Line API response: $result'); // Debug info

      if (result['success']) {
        final List<dynamic> linesData = result['data'] ?? [];
        print('🔍 Retrieved Line count: ${linesData.length}'); // Debug info

        // Batch preload Machine data (Machine API returns downtimeCount)
        final lineIds = linesData.map((line) => line['id'] as int).toList();
        final machinesByLineMap = await cacheService.preloadMachinesByLines(lineIds);

        setState(() {
          _lines = linesData.map((line) {
            final lineId = line['id'] as int;
            final machineResult = machinesByLineMap[lineId] ?? {'success': false, 'data': []};
            final machineCount = machineResult['success'] ? (machineResult['data'] as List).length : 0;
            
            // 🔧 Follow business hierarchy logic: aggregate Line's downtimeCount from Machine's downtimeCount
            int lineDowntimeCount = 0;
            if (machineResult['success']) {
              final machines = machineResult['data'] as List;
              lineDowntimeCount = machines.fold<int>(0, (sum, machine) => sum + (machine['downtimeCount'] as int? ?? 0));
            }

            print('🔍 Line ${line['name']} (ID: $lineId): $machineCount machines, $lineDowntimeCount Downtimes (aggregated from machines)');

            return {
              'id': lineId,
              'name': line['name'],
              'downtimeCount': lineDowntimeCount, // Business data aggregated from machine level
              'color': Colors.green, // Line page uses green, avoiding conflict with Area blue
              'status': line['status'] ?? 'Active',
              'areaId': line['areaId'],
              'machineCount': machineCount, // Use actual machine count
              'position': line['position'],
              'createdDate': line['createdDate'],
            };
          }).toList();
          _isLoading = false;
        });
      } else {
        throw Exception(result['error'] ?? 'Failed to load lines');
      }
    } catch (e) {
      print('❌ Failed to load Line: $e'); // Debug info
      setState(() {
        _error = 'Error loading lines: $e';
        _isLoading = false;
      });
    }
  }

  void _scrollListener() {
    if (_scrollController.offset > 200) {
      if (!_showScrollToTop) {
        setState(() {
          _showScrollToTop = true;
        });
      }
    } else {
      if (_showScrollToTop) {
        setState(() {
          _showScrollToTop = false;
        });
      }
    }
  }

  void _selectLine(BuildContext context, Map<String, dynamic> line) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => MachineSelectionPage(
          lineId: line['id'],
          lineName: line['name'],
          lineColor: line['color'],
          areaName: widget.areaName,
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[100],
      appBar: AppBar(
        title: Text(
          widget.areaName,
          style: const TextStyle(
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: widget.areaColor,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () {
            Navigator.pop(context);
          },
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.home, color: Colors.white),
            onPressed: () {
              Navigator.popUntil(context, (route) => route.isFirst);
            },
          ),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.all(12.0),
        child: _buildBody(),
      ),
      floatingActionButton: _showScrollToTop
          ? FloatingActionButton(
              onPressed: () {
                _scrollController.animateTo(
                  0,
                  duration: const Duration(milliseconds: 500),
                  curve: Curves.easeInOut,
                );
              },
              backgroundColor: widget.areaColor,
              child: const Icon(Icons.keyboard_arrow_up, color: Colors.white),
            )
          : null,
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('Loading lines...', style: TextStyle(fontSize: 16)),
          ],
        ),
      );
    }

    if (_error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, size: 64, color: Colors.red[300]),
            const SizedBox(height: 16),
            Text(
              'Error loading lines',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold, color: Colors.red[700]),
            ),
            const SizedBox(height: 8),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 32),
              child: Text(
                _error!,
                style: TextStyle(fontSize: 14, color: Colors.grey[600]),
                textAlign: TextAlign.center,
              ),
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: _loadLines,
              icon: const Icon(Icons.refresh),
              label: const Text('Retry'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue,
                foregroundColor: Colors.white,
              ),
            ),
          ],
        ),
      );
    }

    if (_lines.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.inbox, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text('No lines found', style: TextStyle(fontSize: 18, color: Colors.grey)),
          ],
        ),
      );
    }

    return _buildGridView();
  }

  Widget _buildGridView() {
    return GridView.builder(
      controller: _scrollController,
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        crossAxisSpacing: 12,
        mainAxisSpacing: 12,
        childAspectRatio: 1.0,
      ),
      itemCount: _lines.length,
      itemBuilder: (context, index) {
        final line = _lines[index];
        
        // Show all lines (can filter later if needed)
        // if (line['downtimeCount'] == 0) {
        //   return const SizedBox.shrink();
        // }

        return Card(
          elevation: 4,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          child: InkWell(
            onTap: () => _selectLine(context, line),
            borderRadius: BorderRadius.circular(12),
            child: Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12),
                gradient: LinearGradient(
                  colors: [
                    line['color'].withOpacity(0.1),
                    line['color'].withOpacity(0.05),
                  ],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
              ),
              child: Stack(
                children: [
                  // Main content - centered
                  Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        // Line icon
                        Container(
                          width: 50,
                          height: 50,
                          decoration: BoxDecoration(
                            color: line['color'].withOpacity(0.2),
                            borderRadius: BorderRadius.circular(25),
                          ),
                          child: Icon(
                            Icons.precision_manufacturing,
                            color: line['color'],
                            size: 26,
                          ),
                        ),
                        const SizedBox(height: 12),
                        
                        // Line name - centered
                        Text(
                          line['name'],
                          style: const TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: Colors.black87,
                          ),
                          textAlign: TextAlign.center,
                        ),
                        const SizedBox(height: 6),
                        
                        // Downtime count info - centered
                        Text(
                          line['downtimeCount'] == 0 ? 'No downtimes' : '${line['downtimeCount']} downtimes',
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey[600],
                            fontWeight: FontWeight.w500,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ),
                  ),
                  
                  // Downtime count badge
                  Positioned(
                    top: 8,
                    right: 8,
                    child: Container(
                      width: 28,
                      height: 28,
                      decoration: BoxDecoration(
                        color: Colors.green, // Consistent with Line page card color
                        shape: BoxShape.circle,
                        boxShadow: [
                          BoxShadow(
                            color: Colors.green.withOpacity(0.3),
                            blurRadius: 4,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: Center(
                        child: Text(
                          '${line['downtimeCount']}',
                          style: const TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                            fontSize: 12,
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }


  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }
}
