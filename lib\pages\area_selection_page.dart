import 'package:flutter/material.dart';
import 'line_selection_page.dart';
import 'login_page.dart';
import '../services/notification_service.dart';
import '../services/data_cache_service.dart';
import '../widgets/smart_loading_widget.dart';

class AreaSelectionPage extends StatefulWidget {
  const AreaSelectionPage({super.key});

  @override
  State<AreaSelectionPage> createState() => _AreaSelectionPageState();
}

class _AreaSelectionPageState extends State<AreaSelectionPage> {
  final ScrollController _scrollController = ScrollController();
  bool _showScrollToTop = false;
  bool _isLoading = true;
  List<Map<String, dynamic>> _areas = [];
  String? _error;

 

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_scrollListener);
    _loadAreas();
    _preloadData();
  }

  
  Future<void> _preloadData() async {
    try {
      final cacheService = DataCacheService();
      print('🚀 Starting to preload complete hierarchy data...');
      
     
      await cacheService.getCompleteHierarchyData();
      
      print('✅ Complete hierarchy data preload finished');
    } catch (e) {
      print('⚠️ Failed to preload data: $e');
      
    }
  }

  Future<void> _loadAreas() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final cacheService = DataCacheService();
      
      
      final areasResult = await cacheService.getAreas();
      
      if (areasResult['success']) {
        final List<dynamic> areasData = areasResult['data'];
        
        print('🔍 Retrieved ${areasData.length} Areas');
        
        setState(() {
          _areas = areasData.map((area) {
            final areaId = area['id'];
            
            final apiDowntimeCount = area['downtimeCount'] ?? 0;
            
            print('🔍 Area ${area['name']} (ID: $areaId): API returned Downtime count = $apiDowntimeCount');
            
            return {
              'id': areaId,
              'name': area['name'],
              'downtimeCount': apiDowntimeCount, 
              'color': Colors.pink, 
              'createdDate': area['createdDate'],
              'position': area['position'] ?? 0,
            };
          }).toList();
          _isLoading = false;
        });
      } else {
        setState(() {
          _error = areasResult['error'] ?? 'Failed to load areas';
          _isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        _error = 'Error loading areas: $e';
        _isLoading = false;
      });
    }
  }

  void _scrollListener() {
    if (_scrollController.offset > 200) {
      if (!_showScrollToTop) {
        setState(() {
          _showScrollToTop = true;
        });
      }
    } else {
      if (_showScrollToTop) {
        setState(() {
          _showScrollToTop = false;
        });
      }
    }
  }

  void _selectArea(BuildContext context, Map<String, dynamic> area) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => LineSelectionPage(
          areaId: area['id'],
          areaName: area['name'],
          areaColor: area['color'],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[100],
      appBar: AppBar(
        title: const Text(
          'Areas',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: Colors.blue,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.logout, color: Colors.white),
          onPressed: () {
            Navigator.pushAndRemoveUntil(
              context,
              MaterialPageRoute(builder: (context) => const LoginPage()),
              (route) => false,
            );
          },
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.home, color: Colors.white),
            onPressed: () {
              Navigator.popUntil(context, (route) => route.isFirst);
            },
          ),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.all(12.0),
        child: _buildBody(),
      ),
      floatingActionButton: _showScrollToTop
          ? FloatingActionButton(
              onPressed: _scrollToTop,
              backgroundColor: Colors.blue,
              child: const Icon(Icons.keyboard_arrow_up, color: Colors.white),
            )
          : null,
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const SmartLoadingWidget(
        loadingText: 'Loading areas...',
        color: Colors.blue,
      );
    }

    if (_error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, size: 64, color: Colors.red[300]),
            const SizedBox(height: 16),
            Text(
              'Error loading areas',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold, color: Colors.red[700]),
            ),
            const SizedBox(height: 8),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 32),
              child: Text(
                _error!,
                style: TextStyle(fontSize: 14, color: Colors.grey[600]),
                textAlign: TextAlign.center,
              ),
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: _loadAreas,
              icon: const Icon(Icons.refresh),
              label: const Text('Retry'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue,
                foregroundColor: Colors.white,
              ),
            ),
          ],
        ),
      );
    }

    if (_areas.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.inbox, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text('No areas found', style: TextStyle(fontSize: 18, color: Colors.grey)),
          ],
        ),
      );
    }

    return _buildGridView();
  }

  Widget _buildGridView() {
    return GridView.builder(
      controller: _scrollController,
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        crossAxisSpacing: 12,
        mainAxisSpacing: 12,
        childAspectRatio: 1.0,
      ),
      itemCount: _areas.length,
      itemBuilder: (context, index) {
        final area = _areas[index];
        
        return Card(
          elevation: 4,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          child: InkWell(
            onTap: () => _selectArea(context, area),
            borderRadius: BorderRadius.circular(12),
            child: Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12),
                gradient: LinearGradient(
                  colors: [
                    area['color'].withOpacity(0.1),
                    area['color'].withOpacity(0.05),
                  ],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
              ),
              child: Stack(
                children: [
                  // Main content - centered
                  Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        // Area icon
                        Container(
                          width: 50,
                          height: 50,
                          decoration: BoxDecoration(
                            color: area['color'].withOpacity(0.2),
                            borderRadius: BorderRadius.circular(25),
                          ),
                          child: Icon(
                            Icons.factory,
                            color: area['color'],
                            size: 26,
                          ),
                        ),
                        const SizedBox(height: 12),
                        
                        // Area name - centered
                        Text(
                          area['name'],
                          style: const TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: Colors.black87,
                          ),
                          textAlign: TextAlign.center,
                        ),
                        const SizedBox(height: 6),
                        
                        // Downtime count info - centered
                        Text(
                          area['downtimeCount'] == 0 ? 'No downtimes' : '${area['downtimeCount']} downtimes',
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey[600],
                            fontWeight: FontWeight.w500,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ),
                  ),
                  
                  // Downtime count badge
                  Positioned(
                    top: 8,
                    right: 8,
                    child: Container(
                      width: 28,
                      height: 28,
                      decoration: BoxDecoration(
                        color: Colors.pink, // Consistent with Area page card color
                        shape: BoxShape.circle,
                        boxShadow: [
                          BoxShadow(
                            color: Colors.pink.withOpacity(0.3),
                            blurRadius: 4,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: Center(
                        child: Text(
                          '${area['downtimeCount']}',
                          style: const TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                            fontSize: 12,
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }


  void _scrollToTop() {
    _scrollController.animateTo(
      0,
      duration: const Duration(milliseconds: 500),
      curve: Curves.easeInOut,
    );
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }
}
