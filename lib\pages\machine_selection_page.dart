import 'package:flutter/material.dart';
import 'downtime_selection_page.dart';
import '../services/data_cache_service.dart';

class MachineSelectionPage extends StatefulWidget {
  final int lineId;
  final String lineName;
  final Color lineColor;
  final String? areaName;

  const MachineSelectionPage({
    super.key,
    required this.lineId,
    required this.lineName,
    required this.lineColor,
    this.areaName,
  });

  @override
  State<MachineSelectionPage> createState() => _MachineSelectionPageState();
}

class _MachineSelectionPageState extends State<MachineSelectionPage> {
  final ScrollController _scrollController = ScrollController();
  bool _showScrollToTop = false;
  bool _isLoading = true;
  List<Map<String, dynamic>> _machines = [];
  String? _error;

  // Note: Machine colors are now managed individually per machine

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_scrollListener);
    _loadMachines();
  }

  Future<void> _loadMachines() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final cacheService = DataCacheService();
      
    
      final machinesResult = await cacheService.getMachinesByLine(widget.lineId);
      
      if (machinesResult['success']) {
        final List<dynamic> machinesData = machinesResult['data'];
        
        print('🔍 Retrieved ${machinesData.length} Machines');
        
        setState(() {
          _machines = machinesData.map((machine) {
            final machineId = machine['id'];
            // 🔧 Follow business hierarchy logic: directly use downtimeCount returned by Machine API
            final apiDowntimeCount = machine['downtimeCount'] ?? 0;
            
            print('🔍 Machine ${machine['name']} (ID: $machineId): API returned Downtime count = $apiDowntimeCount');
            
            return {
              'id': machineId,
              'name': machine['name'],
              'machineStatus': machine['machineStatus'],
              'machineStatus_Lastupdated': machine['machineStatus_Lastupdated'],
              'lineId': machine['lineId'],
              'downtimeCount': apiDowntimeCount, // Directly use business data returned by API
              'color': Colors.orange, // Supervisor requirement: Machine page uses orange uniformly (avoiding conflict with Area blue)
              'createdDate': machine['createdDate'],
            };
          }).toList();
          _isLoading = false;
        });
      } else {
        setState(() {
          _error = machinesResult['error'] ?? 'Failed to load machines';
          _isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        _error = 'Error loading machines: $e';
        _isLoading = false;
      });
    }
  }

  void _scrollListener() {
    if (_scrollController.offset > 200) {
      if (!_showScrollToTop) {
        setState(() {
          _showScrollToTop = true;
        });
      }
    } else {
      if (_showScrollToTop) {
        setState(() {
          _showScrollToTop = false;
        });
      }
    }
  }

  void _selectMachine(BuildContext context, Map<String, dynamic> machine) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => DowntimeSelectionPage(
          machineId: machine['id'],
          machineName: machine['name'],
          downtimeCount: machine['downtimeCount'],
          machineColor: machine['color'],
          lineName: widget.lineName,
          areaName: widget.areaName,
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[100],
      appBar: AppBar(
        title: Text(
          widget.lineName,
          style: const TextStyle(
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: widget.lineColor, // Inherit Line page color (green)
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.pop(context),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.home, color: Colors.white),
            onPressed: () {
              Navigator.popUntil(context, (route) => route.isFirst);
            },
          ),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.all(12.0),
        child: _buildBody(),
      ),
      floatingActionButton: _showScrollToTop
          ? FloatingActionButton(
              onPressed: _scrollToTop,
              backgroundColor: widget.lineColor, // Consistent with Machine page AppBar color (green)
              child: const Icon(Icons.keyboard_arrow_up, color: Colors.white),
            )
          : null,
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('Loading machines...', style: TextStyle(fontSize: 16)),
          ],
        ),
      );
    }

    if (_error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, size: 64, color: Colors.red[300]),
            const SizedBox(height: 16),
            Text(
              'Error loading machines',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold, color: Colors.red[700]),
            ),
            const SizedBox(height: 8),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 32),
              child: Text(
                _error!,
                style: TextStyle(fontSize: 14, color: Colors.grey[600]),
                textAlign: TextAlign.center,
              ),
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: _loadMachines,
              icon: const Icon(Icons.refresh),
              label: const Text('Retry'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue,
                foregroundColor: Colors.white,
              ),
            ),
          ],
        ),
      );
    }

    if (_machines.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.precision_manufacturing, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text('No machines found', style: TextStyle(fontSize: 18, color: Colors.grey)),
          ],
        ),
      );
    }

    return _buildGridView();
  }

  Widget _buildGridView() {
    return GridView.builder(
      controller: _scrollController,
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        crossAxisSpacing: 12,
        mainAxisSpacing: 12,
        childAspectRatio: 1.0,
      ),
      itemCount: _machines.length,
      itemBuilder: (context, index) {
        final machine = _machines[index];
        
        return Card(
          elevation: 4,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          child: InkWell(
            onTap: () => _selectMachine(context, machine),
            borderRadius: BorderRadius.circular(12),
            child: Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12),
                gradient: LinearGradient(
                  colors: [
                    machine['color'].withOpacity(0.1),
                    machine['color'].withOpacity(0.05),
                  ],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
              ),
              child: Stack(
                children: [
                  // Main content - centered
                  Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        // Machine icon
                        Container(
                          width: 50,
                          height: 50,
                          decoration: BoxDecoration(
                            color: machine['color'].withOpacity(0.2),
                            borderRadius: BorderRadius.circular(25),
                          ),
                          child: Icon(
                            Icons.precision_manufacturing,
                            color: machine['color'],
                            size: 26,
                          ),
                        ),
                        const SizedBox(height: 12),
                        
                        // Machine name - centered
                        Text(
                          machine['name'],
                          style: const TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: Colors.black87,
                          ),
                          textAlign: TextAlign.center,
                        ),
                        const SizedBox(height: 6),
                        
                        // Status info - centered
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                          decoration: BoxDecoration(
                            color: machine['machineStatus'] == 'Active' ? Colors.green : Colors.red,
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Text(
                            machine['machineStatus'] ?? 'Unknown',
                            style: const TextStyle(
                              fontSize: 10,
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  
                  // Downtime count badge
                  Positioned(
                    top: 8,
                    right: 8,
                    child: Container(
                      width: 28,
                      height: 28,
                      decoration: BoxDecoration(
                        color: Colors.orange, // Consistent with Machine page card color
                        shape: BoxShape.circle,
                        boxShadow: [
                          BoxShadow(
                            color: Colors.orange.withOpacity(0.3),
                            blurRadius: 4,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: Center(
                        child: Text(
                          '${machine['downtimeCount']}',
                          style: const TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                            fontSize: 12,
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  void _scrollToTop() {
    _scrollController.animateTo(
      0,
      duration: const Duration(milliseconds: 500),
      curve: Curves.easeInOut,
    );
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }
}
