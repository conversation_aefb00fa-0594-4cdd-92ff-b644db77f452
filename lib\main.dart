import 'package:flutter/material.dart';
import 'pages/login_page.dart';
import 'services/system_notification_service.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  // Initialize system notification service
  await SystemNotificationService().initialize();
  
  runApp(const TicketSystemApp());
}

class TicketSystemApp extends StatelessWidget {
  const TicketSystemApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Ticket System',
      theme: ThemeData(
        primarySwatch: Colors.blue,
        visualDensity: VisualDensity.adaptivePlatformDensity,
      ),
      home: const LoginPage(),
      debugShowCheckedModeBanner: false,
    );
  }
}
