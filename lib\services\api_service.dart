import 'dart:convert';
import 'package:http/http.dart' as http;

class ApiService {
  // Base URL - can be configured in Settings page
  static String _baseUrl = 'https://qc.sophicauto.net/IRocovery_API';
  
  // Get current API base URL
  static String get baseUrl => _baseUrl;
  
  // Set new API base URL (for Settings page)
  static void setBaseUrl(String newUrl) {
    _baseUrl = newUrl.endsWith('/') ? newUrl.substring(0, newUrl.length - 1) : newUrl;
  }

  // Generic HTTP request method
  static Future<Map<String, dynamic>> _makeRequest(String endpoint) async {
    try {
      final url = Uri.parse('$_baseUrl$endpoint');
      print('Making API request to: $url'); // Debug
      
      final response = await http.get(
        url,
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
      ).timeout(const Duration(seconds: 10));

      print('Response status: ${response.statusCode}'); // Debug
      print('Response body: ${response.body}'); // Debug

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return {
          'success': true,
          'data': data,
        };
      } else {
        return {
          'success': false,
          'error': 'HTTP ${response.statusCode}: ${response.reasonPhrase}',
        };
      }
    } catch (e) {
      print('API Error: $e'); // Debug
      return {
        'success': false,
        'error': 'Network error: $e',
      };
    }
  }

  // 1. Get all Areas and downtime counts - according to TXT document
  static Future<Map<String, dynamic>> getAreas() async {
    return await _makeRequest('/area');
  }

  // 2. Get Line list under specified Area - according to TXT document
  static Future<Map<String, dynamic>> getLinesByArea(int areaId) async {
    return await _makeRequest('/line/$areaId');
  }

  // 3. Get Machine list and downtime counts under specified Line - according to TXT document
  static Future<Map<String, dynamic>> getMachinesByLine(int lineId) async {
    return await _makeRequest('/machine/downtimeCount/$lineId');
  }

  // 4. Get all Downtimes - using Swagger API
  static Future<Map<String, dynamic>> getAllDowntimes() async {
    return await _makeRequest('/Downtime');
  }

  // 5. Get Overall Downtimes by Area - using Swagger API  
  static Future<Map<String, dynamic>> getOverallDowntimeByArea(int areaId) async {
    return await _makeRequest('/Downtime/GetOverallDowntimeByArea/$areaId');
  }

  // 6. Get My Downtimes by Area - using Swagger API
  static Future<Map<String, dynamic>> getMyDowntimeByArea(int areaId) async {
    return await _makeRequest('/Downtime/GetMyDowntimeByArea/$areaId');
  }

  // 7. Get Downtime details - try using Swagger API
  static Future<Map<String, dynamic>> getDowntimeDetails(String downtimeId) async {
    try {
      // Try using GET /Downtime/{id} API (if exists)
      return await _makeRequest('/Downtime/$downtimeId');
    } catch (e) {
      print('❌ Downtime details API failed, returning error: $e');
      // If API doesn't exist or fails, return failure response instead of fake data
      return {
        'success': false,
        'message': 'Downtime details API not available or failed: $e',
        'data': null,
      };
    }
  }

  // === The following are Swagger APIs (kept for testing) ===
  // Get detailed information of specified Area
  static Future<Map<String, dynamic>> getAreaById(int areaId) async {
    return await _makeRequest('/Area/GetById/$areaId');
  }

  // Get information of specified Line (for adding downtime)
  static Future<Map<String, dynamic>> getLineForDowntime(int lineId) async {
    return await _makeRequest('/Line/GetLineForAddDowntime/$lineId');
  }

  // Get basic information of specified Line
  static Future<Map<String, dynamic>> getLineById(int lineId) async {
    return await _makeRequest('/Line/$lineId');
  }

  // Test API connection
  static Future<bool> testConnection() async {
    try {
      final result = await getAreas();
      return result['success'] == true;
    } catch (e) {
      return false;
    }
  }
}
