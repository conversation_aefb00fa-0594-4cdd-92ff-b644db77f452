import 'package:flutter/foundation.dart';
import 'api_service.dart';

/// Data cache service - avoid duplicate API calls, improve performance
class DataCacheService {
  static final DataCacheService _instance = DataCacheService._internal();
  factory DataCacheService() => _instance;
  DataCacheService._internal();

  // Cache storage
  final Map<String, dynamic> _cache = {};
  final Map<String, DateTime> _cacheTimestamps = {};
  
  // 缓存过期时间（5分钟）
  static const Duration _cacheExpiry = Duration(minutes: 5);

  /// 检查缓存是否有效
  bool _isCacheValid(String key) {
    if (!_cache.containsKey(key) || !_cacheTimestamps.containsKey(key)) {
      return false;
    }
    
    final timestamp = _cacheTimestamps[key]!;
    final now = DateTime.now();
    return now.difference(timestamp) < _cacheExpiry;
  }

  /// 设置缓存
  void _setCache(String key, dynamic data) {
    _cache[key] = data;
    _cacheTimestamps[key] = DateTime.now();
    debugPrint('📦 缓存已设置: $key');
  }

  /// 获取缓存
  T? _getCache<T>(String key) {
    if (_isCacheValid(key)) {
      debugPrint('✅ 使用缓存: $key');
      return _cache[key] as T?;
    }
    debugPrint('❌ 缓存失效或不存在: $key');
    return null;
  }

  /// 清除指定缓存
  void clearCache(String key) {
    _cache.remove(key);
    _cacheTimestamps.remove(key);
    debugPrint('🗑️ 清除缓存: $key');
  }

  /// 清除所有缓存
  void clearAllCache() {
    _cache.clear();
    _cacheTimestamps.clear();
    debugPrint('🗑️ 清除所有缓存');
  }

  /// 获取Areas数据（带缓存）
  Future<Map<String, dynamic>> getAreas() async {
    const cacheKey = 'areas';
    
    // 尝试从缓存获取
    final cachedData = _getCache<Map<String, dynamic>>(cacheKey);
    if (cachedData != null) {
      return cachedData;
    }

    // 缓存未命中，调用API
    debugPrint('🌐 API调用: getAreas');
    final result = await ApiService.getAreas();
    
    if (result['success']) {
      _setCache(cacheKey, result);
    }
    
    return result;
  }

  /// 获取所有Downtimes数据（带缓存）
  Future<Map<String, dynamic>> getAllDowntimes() async {
    const cacheKey = 'all_downtimes';
    
    // 尝试从缓存获取
    final cachedData = _getCache<Map<String, dynamic>>(cacheKey);
    if (cachedData != null) {
      return cachedData;
    }

    // 缓存未命中，调用API
    debugPrint('🌐 API调用: getAllDowntimes');
    final result = await ApiService.getAllDowntimes();
    
    if (result['success']) {
      _setCache(cacheKey, result);
    }
    
    return result;
  }

  /// 获取指定Area的Lines数据（带缓存）
  Future<Map<String, dynamic>> getLinesByArea(int areaId) async {
    final cacheKey = 'lines_area_$areaId';
    
    // 尝试从缓存获取
    final cachedData = _getCache<Map<String, dynamic>>(cacheKey);
    if (cachedData != null) {
      return cachedData;
    }

    // 缓存未命中，调用API
    debugPrint('🌐 API调用: getLinesByArea($areaId)');
    final result = await ApiService.getLinesByArea(areaId);
    
    if (result['success']) {
      _setCache(cacheKey, result);
    }
    
    return result;
  }

  /// 获取指定Line的Machines数据（带缓存）
  Future<Map<String, dynamic>> getMachinesByLine(int lineId) async {
    final cacheKey = 'machines_line_$lineId';
    
    // 尝试从缓存获取
    final cachedData = _getCache<Map<String, dynamic>>(cacheKey);
    if (cachedData != null) {
      return cachedData;
    }

    // 缓存未命中，调用API
    debugPrint('🌐 API调用: getMachinesByLine($lineId)');
    final result = await ApiService.getMachinesByLine(lineId);
    
    if (result['success']) {
      _setCache(cacheKey, result);
    }
    
    return result;
  }

  /// 批量预加载机器数据（优化Line页面性能）
  Future<Map<int, Map<String, dynamic>>> preloadMachinesByLines(List<int> lineIds) async {
    final Map<int, Map<String, dynamic>> results = {};
    
    // 检查哪些数据需要从API获取
    final List<int> uncachedLineIds = [];
    for (final lineId in lineIds) {
      final cacheKey = 'machines_line_$lineId';
      final cachedData = _getCache<Map<String, dynamic>>(cacheKey);
      
      if (cachedData != null) {
        results[lineId] = cachedData;
      } else {
        uncachedLineIds.add(lineId);
      }
    }

    // 批量获取未缓存的数据
    if (uncachedLineIds.isNotEmpty) {
      debugPrint('🌐 批量API调用: getMachinesByLine for ${uncachedLineIds.length} lines');
      
      final futures = uncachedLineIds.map((lineId) => getMachinesByLine(lineId));
      final apiResults = await Future.wait(futures);
      
      for (int i = 0; i < uncachedLineIds.length; i++) {
        final lineId = uncachedLineIds[i];
        results[lineId] = apiResults[i];
      }
    }

    return results;
  }

  /// 获取完整的层级数据（Area -> Lines -> Machines -> Downtimes）
  Future<Map<String, dynamic>> getCompleteHierarchyData() async {
    const cacheKey = 'complete_hierarchy';
    
    // 尝试从缓存获取
    final cachedData = _getCache<Map<String, dynamic>>(cacheKey);
    if (cachedData != null) {
      return cachedData;
    }

    debugPrint('🌐 获取完整层级数据...');

    try {
      // 并行获取基础数据
      final futures = await Future.wait([
        getAreas(),
        getAllDowntimes(),
      ]);

      final areasResult = futures[0];
      final downtimesResult = futures[1];

      if (!areasResult['success'] || !downtimesResult['success']) {
        return {
          'success': false,
          'error': 'Failed to load basic data',
        };
      }

      final areasData = areasResult['data'] as List<dynamic>;
      final downtimesData = downtimesResult['data'] as List<dynamic>;

      // 为每个Area获取其Lines数据
      final Map<int, List<dynamic>> areaLinesMap = {};
      final List<int> allLineIds = [];

      for (final area in areasData) {
        final areaId = area['id'] as int;
        final linesResult = await getLinesByArea(areaId);
        
        if (linesResult['success']) {
          final lines = linesResult['data'] as List<dynamic>;
          areaLinesMap[areaId] = lines;
          
          // 收集所有Line ID用于批量获取Machine数据
          for (final line in lines) {
            allLineIds.add(line['id'] as int);
          }
        }
      }

      // 批量预加载所有Machine数据
      final machinesByLineMap = await preloadMachinesByLines(allLineIds);

      final result = {
        'success': true,
        'data': {
          'areas': areasData,
          'downtimes': downtimesData,
          'areaLinesMap': areaLinesMap,
          'machinesByLineMap': machinesByLineMap,
        }
      };

      _setCache(cacheKey, result);
      debugPrint('✅ 完整层级数据已缓存');

      return result;
    } catch (e) {
      debugPrint('❌ 获取完整层级数据失败: $e');
      return {
        'success': false,
        'error': 'Error loading hierarchy data: $e',
      };
    }
  }

  /// 刷新指定类型的缓存
  Future<void> refreshCache(String type) async {
    switch (type) {
      case 'areas':
        clearCache('areas');
        await getAreas();
        break;
      case 'downtimes':
        clearCache('all_downtimes');
        await getAllDowntimes();
        break;
      case 'hierarchy':
        clearCache('complete_hierarchy');
        await getCompleteHierarchyData();
        break;
    }
  }

  /// 获取Downtime详情数据（带缓存）
  Future<Map<String, dynamic>> getDowntimeDetails(String downtimeId) async {
    final cacheKey = 'downtime_detail_$downtimeId';
    
    // 尝试从缓存获取
    final cachedData = _getCache<Map<String, dynamic>>(cacheKey);
    if (cachedData != null) {
      return cachedData;
    }

    // 缓存未命中，调用API
    debugPrint('🌐 API调用: getDowntimeDetails($downtimeId)');
    final result = await ApiService.getDowntimeDetails(downtimeId);
    
    if (result['success']) {
      _setCache(cacheKey, result);
    }
    
    return result;
  }

  /// 清除Downtime相关的缓存
  void clearDowntimeCache() {
    final keysToRemove = _cache.keys.where((key) => 
      key.startsWith('downtime_detail_') || key == 'all_downtimes'
    ).toList();
    
    for (final key in keysToRemove) {
      clearCache(key);
    }
    
    debugPrint('🗑️ 清除Downtime相关缓存: ${keysToRemove.length} 项');
  }

  /// 获取缓存统计信息
  Map<String, dynamic> getCacheStats() {
    final stats = {
      'totalCacheItems': _cache.length,
      'cacheKeys': _cache.keys.toList(),
      'timestamps': _cacheTimestamps,
    };
    
    debugPrint('📊 缓存统计: ${stats['totalCacheItems']} 项');
    return stats;
  }
}
