import 'package:flutter/material.dart';
import '../services/data_cache_service.dart';
import '../widgets/smart_loading_widget.dart';

class DowntimeDetailPage extends StatefulWidget {
  final Map<String, dynamic> downtime;
  final String machineName;
  final Color machineColor;
  final String? lineName;
  final String? areaName;

  const DowntimeDetailPage({
    super.key,
    required this.downtime,
    required this.machineName,
    required this.machineColor,
    this.lineName,
    this.areaName,
  });

  @override
  State<DowntimeDetailPage> createState() => _DowntimeDetailPageState();
}

class _DowntimeDetailPageState extends State<DowntimeDetailPage> {
  Map<String, dynamic>? _detailedInfo;
  bool _isLoading = true;
  String? _error;

  @override
  void initState() {
    super.initState();
   
    _initializeBasicInfo();
    _loadDowntimeDetails();
  }

 
  void _initializeBasicInfo() {
    _detailedInfo = {
      'status': widget.downtime['status'] ?? 'LOCK',
      'startDate': widget.downtime['createdDate'] ?? DateTime.now().toIso8601String(),
      'totalTime': 'Calculating...',
      'errorDescription': widget.downtime['description'] ?? 'Machine downtime issue',
      'lotId': widget.downtime['rawData']?['lotId'] ?? 'N/A',
      'currentRecipe': widget.downtime['rawData']?['currentRecipe'] ?? 'N/A',
      'remarks': widget.downtime['rawData']?['remark'] ?? '',
      'errorCodeRemark': widget.downtime['rawData']?['errorCodeRemark'] ?? 'N/A',
      'machineId': widget.downtime['machineId'],
      'downtimeType': widget.downtime['title'] ?? 'Downtime Issue',
    };
    _isLoading = false; 
  }

  Future<void> _loadDowntimeDetails() async {
    
    try {
      final cacheService = DataCacheService();
      final result = await cacheService.getDowntimeDetails(widget.downtime['id'].toString());
      print('🔍 Downtime details API response: $result');

      if (result['success'] && result['data'] != null && mounted) {
    
        setState(() {
          _detailedInfo = result['data'];
        });
        print('✅ Details API success, display updated');
      } else {
        print('⚠️ Details API no data, continue using basic info');
      }
    } catch (e) {
      print('⚠️ Details API failed, continue using basic info: $e');
    
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return Scaffold(
        backgroundColor: const Color(0xFFF5F7FA),
        appBar: AppBar(
          title: Text(
            '${widget.downtime['id']} - ${widget.machineName}',
            style: const TextStyle(
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
          backgroundColor: widget.machineColor,
          elevation: 0,
          leading: IconButton(
            icon: const Icon(Icons.arrow_back, color: Colors.white),
            onPressed: () => Navigator.pop(context),
          ),
        ),
        body: const SmartLoadingWidget(
          loadingText: 'Loading downtime details...',
          color: Colors.purple,
        ),
      );
    }

    if (_detailedInfo == null) {
      return Scaffold(
        backgroundColor: const Color(0xFFF5F7FA),
        appBar: AppBar(
          title: Text(
            '${widget.downtime['id']} - ${widget.machineName}',
            style: const TextStyle(
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
          backgroundColor: widget.machineColor,
          elevation: 0,
          leading: IconButton(
            icon: const Icon(Icons.arrow_back, color: Colors.white),
            onPressed: () => Navigator.pop(context),
          ),
        ),
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(Icons.error_outline, size: 64, color: Colors.red),
              const SizedBox(height: 16),
              const Text('Error loading downtime details', style: TextStyle(fontSize: 18, color: Colors.red)),
              if (_error != null) ...[
                const SizedBox(height: 8),
                Text(_error!, style: const TextStyle(fontSize: 14, color: Colors.grey)),
              ],
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: _loadDowntimeDetails,
                child: const Text('Retry'),
              ),
            ],
          ),
        ),
      );
    }

    final detailedInfo = _detailedInfo!;
    
    return Scaffold(
      backgroundColor: const Color(0xFFF5F7FA),
      appBar: AppBar(
        title: Text(
          '${widget.downtime['id']} - ${widget.machineName}',
          style: const TextStyle(
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: widget.machineColor,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.pop(context),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.home, color: Colors.white),
            onPressed: () {
              Navigator.popUntil(context, (route) => route.isFirst);
            },
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header Card with status
            Card(
              elevation: 2,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(16),
              ),
              child: Container(
                width: double.infinity,
                padding: const EdgeInsets.all(24),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(16),
                  gradient: LinearGradient(
                    colors: [
                      widget.machineColor.withOpacity(0.08),
                      widget.machineColor.withOpacity(0.03),
                    ],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.04),
                      blurRadius: 10,
                      offset: const Offset(0, 4),
                    ),
                  ],
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Area, Line and Machine badges
                    Wrap(
                      spacing: 8,
                      runSpacing: 8,
                      children: [
                        if (widget.areaName != null) 
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 12,
                              vertical: 6,
                            ),
                            decoration: BoxDecoration(
                              color: Colors.orange,
                              borderRadius: BorderRadius.circular(20),
                            ),
                            child: Text(
                              widget.areaName!,
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: 12,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        if (widget.lineName != null)
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 12,
                              vertical: 6,
                            ),
                            decoration: BoxDecoration(
                              color: Colors.blue,
                              borderRadius: BorderRadius.circular(20),
                            ),
                            child: Text(
                              widget.lineName!,
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: 12,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 12,
                            vertical: 6,
                          ),
                          decoration: BoxDecoration(
                            color: widget.machineColor,
                            borderRadius: BorderRadius.circular(20),
                          ),
                          child: Text(
                            widget.machineName,
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ],
                    ),
                    
                    const SizedBox(height: 16),
                    
                    // Downtime ID and Status
                    Text(
                      '${widget.downtime['id']} STATUS: ${detailedInfo['status']}',
                      style: const TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: Color(0xFF1A202C),
                      ),
                    ),
                    
                    const SizedBox(height: 8),
                    
                    // Downtime type
                    Text(
                      'Type: ${detailedInfo['downtimeType']}',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: Colors.grey[700],
                      ),
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 20),

            // Details Card
            Card(
              elevation: 1,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(16),
              ),
              child: Container(
                width: double.infinity,
                padding: const EdgeInsets.all(24),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(16),
                  gradient: const LinearGradient(
                    colors: [
                      Color(0xFFFEFEFE),
                      Color(0xFFF9FAFB),
                    ],
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.02),
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildSimpleDetailRow('Start Date:', detailedInfo['startDate']),
                  _buildSimpleDetailRow('Total Time:', detailedInfo['totalTime']),
                  _buildSimpleDetailRow('Error Description:', detailedInfo['errorDescription']),
                  _buildSimpleDetailRow('LotId:', detailedInfo['lotId']),
                  _buildSimpleDetailRow('Current Recipe:', detailedInfo['currentRecipe'].isEmpty ? '-' : detailedInfo['currentRecipe']),
                  _buildSimpleDetailRow('Remarks:', detailedInfo['remarks'].isEmpty ? '-' : detailedInfo['remarks']),
                  _buildSimpleDetailRow('Error Code Remark:', detailedInfo['errorCodeRemark']),
                  _buildSimpleDetailRow('Machine ID:', detailedInfo['machineId'].toString()),
                ],
              ),
              ),
            ),

            const SizedBox(height: 30),

            // ATTEND Action Button
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: () {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('Attending to ${widget.machineName} downtime ${widget.downtime['id']}...'),
                      backgroundColor: Colors.orange,
                    ),
                  );
                },
                icon: const Icon(Icons.build, color: Colors.white),
                label: const Text(
                  'ATTEND TO MACHINE',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.orange,
                  padding: const EdgeInsets.symmetric(vertical: 18),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  elevation: 4,
                ),
              ),
            ),

            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }

  Widget _buildSimpleDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            label,
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Color(0xFF2D3748),
              letterSpacing: 0.3,
            ),
          ),
          const SizedBox(height: 6),
          Container(
            padding: const EdgeInsets.symmetric(vertical: 2),
            child: Text(
              value,
              style: const TextStyle(
                fontSize: 15,
                color: Color(0xFF4A5568),
                height: 1.4,
                letterSpacing: 0.1,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
